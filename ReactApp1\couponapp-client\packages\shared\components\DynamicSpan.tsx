import React from 'react';
import { DynamicText } from '@repo/shared/lib/types/widgetSettings';
import { useLiveValue } from '@repo/shared/lib/dynamic-values/hooks/useLiveValue';

interface DynamicSpanProps {
  dynamicText: DynamicText;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLSpanElement>) => void;
  'data-editor-selectable-key'?: string;
}


export const DynamicSpan: React.FC<DynamicSpanProps> = ({
  dynamicText,
  className,
  style,
  onClick,
  'data-editor-selectable-key': dataEditorSelectableKey,
}) => {
  const value = useLiveValue(dynamicText.expression, '');

  return (
    <span
      className={className}
      style={style}
      onClick={onClick}
      data-editor-selectable-key={dataEditorSelectableKey}
    >
      {value}
    </span>
  );
};

export default DynamicSpan;
