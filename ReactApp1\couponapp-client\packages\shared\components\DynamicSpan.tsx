import React from 'react';
import { DynamicText } from '@repo/shared/lib/types/widgetSettings';
import { renderExpression } from '@repo/shared/lib/dynamic-values/expression-parser';
import { useDynamicValues } from '@repo/shared/lib/dynamic-values/hooks/useDynamicValues';

interface DynamicSpanProps {
  dynamicText: DynamicText;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLSpanElement>) => void;
  'data-editor-selectable-key'?: string;
}


export const DynamicSpan: React.FC<DynamicSpanProps> = ({
  dynamicText,
  className,
  style,
  onClick,
  'data-editor-selectable-key': dataEditorSelectableKey,
}) => {
  const { getValue } = useDynamicValues();

  // Render the expression with current dynamic values
  const renderedText = renderExpression(
    dynamicText.expression,
    getValue,
    '' // Use empty string when dynamic values are not available
  );

  return (
    <span
      className={className}
      style={style}
      onClick={onClick}
      data-editor-selectable-key={dataEditorSelectableKey}
    >
      {renderedText}
    </span>
  );
};

export default DynamicSpan;
