import React from 'react';
import { DynamicText } from '@repo/shared/lib/types/widgetSettings';
import { renderExpression } from '@repo/shared/lib/dynamic-values/expression-parser';

interface DynamicSpanProps {
  /**
   * The dynamic text configuration containing expression and fallback value
   */
  dynamicText: DynamicText;
  
  /**
   * Additional CSS classes to apply to the span
   */
  className?: string;
  
  /**
   * Additional inline styles to apply to the span
   */
  style?: React.CSSProperties;
  
  /**
   * Click handler for the span
   */
  onClick?: (e: React.MouseEvent<HTMLSpanElement>) => void;
  
  /**
   * Data attribute for editor selection (for config key targeting)
   */
  'data-editor-selectable-key'?: string;
}

/**
 * DynamicSpan component that renders dynamic text with live value resolution
 * 
 * This component takes a DynamicText object and renders it as a span element,
 * automatically resolving any dynamic variables in the expression using the
 * dynamic values system.
 * 
 * @example
 * ```tsx
 * <DynamicSpan
 *   dynamicText={{
 *     expression: "Score: {game/123456/score} | Lives: {game/123456/lives}"
 *   }}
 *   className="text-lg font-bold"
 * />
 * ```
 */
export const DynamicSpan: React.FC<DynamicSpanProps> = ({
  dynamicText,
  className,
  style,
  onClick,
  'data-editor-selectable-key': dataEditorSelectableKey,
}) => {
  const { getValue } = useDynamicValuesContext();
  
  // Render the expression with current dynamic values
  const renderedText = renderExpression(
    dynamicText.expression,
    getValue,
    '' // Use empty string when dynamic values are not available
  );
  
  return (
    <span
      className={className}
      style={style}
      onClick={onClick}
      data-editor-selectable-key={dataEditorSelectableKey}
    >
      {renderedText}
    </span>
  );
};

export default DynamicSpan;
