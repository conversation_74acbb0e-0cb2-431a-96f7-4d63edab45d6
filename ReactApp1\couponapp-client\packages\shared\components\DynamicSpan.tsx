import React, { useMemo } from 'react';
import { DynamicText } from '@repo/shared/lib/types/widgetSettings';
import { renderExpression, extractVariablePaths } from '@repo/shared/lib/dynamic-values/expression-parser';
import { useLiveValues } from '@repo/shared/lib/dynamic-values/hooks/useLiveValue';

interface DynamicSpanProps {
  dynamicText: DynamicText;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLSpanElement>) => void;
  'data-editor-selectable-key'?: string;
}


export const DynamicSpan: React.FC<DynamicSpanProps> = ({
  dynamicText,
  className,
  style,
  onClick,
  'data-editor-selectable-key': dataEditorSelectableKey,
}) => {
  // Extract all variable paths from the expression
  const variablePaths = useMemo(() => {
    return extractVariablePaths(dynamicText.expression);
  }, [dynamicText.expression]);

  // Subscribe to all dynamic values used in the expression
  const dynamicValues = useLiveValues(variablePaths);

  // Create a getValue function that uses the live values
  const getValue = useMemo(() => {
    return (path: string) => dynamicValues[path];
  }, [dynamicValues]);

  // Render the expression with current dynamic values
  const renderedText = useMemo(() => {
    return renderExpression(
      dynamicText.expression,
      getValue,
      '' // Use empty string when dynamic values are not available
    );
  }, [dynamicText.expression, getValue]);

  return (
    <span
      className={className}
      style={style}
      onClick={onClick}
      data-editor-selectable-key={dataEditorSelectableKey}
    >
      {renderedText}
    </span>
  );
};

export default DynamicSpan;
